package com.xc.boot.modules.goods.model.vo;

import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "货品分页列表VO")
public class GoodsPageVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "所属商户ID")
    private Long companyId;

    @Schema(description = "所属门店ID")
    private Long merchantId;

    @Schema(description = "所属门店")
    @GoodsColumn(value = "merchant_id")
    private String merchant;

    @Schema(description = "货品图片")
    private String image;

    @Schema(description = "所属柜台ID")
    private Long counterId;

    @Schema(description = "柜台")
    @GoodsColumn(value = "counter_id")
    private String counter;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商")
    @GoodsColumn(value = "supplier_id")
    private String supplier;

    @Schema(description = "所属大类ID")
    private Long categoryId;

    @Schema(description = "所属大类")
    @GoodsColumn(value = "category_id")
    private String category;

    @Schema(description = "所属小类ID")
    private Long subclassId;

    @Schema(description = "所属小类")
    @GoodsColumn(value = "subclass_id")
    private String subclass;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "品牌")
    @GoodsColumn(value = "brand_id")
    private String brand;

    @Schema(description = "款式ID")
    private Long styleId;

    @Schema(description = "款式")
    @GoodsColumn(value = "style_id")
    private String style;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色")
    @GoodsColumn(value = "quality_id")
    private String quality;

    @Schema(description = "工艺ID")
    private Long technologyId;

    @Schema(description = "工艺")
    @GoodsColumn(value = "technology_id")
    private String technology;

    @Schema(description = "主石ID")
    private Long mainStoneId;

    @Schema(description = "主石")
    @GoodsColumn(value = "main_stone_id")
    private String mainStone;

    @Schema(description = "辅石ID")
    private Long subStoneId;

    @Schema(description = "辅石")
    @GoodsColumn(value = "sub_stone_id")
    private String subStone;

    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn")
    private String goodsSn;

    @Schema(description = "货品名称")
    @GoodsColumn(value = "name")
    private String name;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @GoodsColumn(value = "sales_type")
    private Integer salesType;

    @Schema(description = "批次号")
    @GoodsColumn(value = "batch_no")
    private String batchNo;

    @Schema(description = "证书号")
    @GoodsColumn(value = "cert_no")
    private String certNo;

    @Schema(description = "备注")
    @GoodsColumn(value = "remark")
    private String remark;

    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")

    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal netSilverWeight;

    @Schema(description = "主石数")
    @GoodsColumn(value = "main_stone_count")
    private String mainStoneCount;

    @Schema(description = "主石重(ct)")
    @GoodsColumn(value = "main_stone_weight")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石数")
    @GoodsColumn(value = "sub_stone_count")
    private String subStoneCount;

    @Schema(description = "辅石重(ct)")
    @GoodsColumn(value = "sub_stone_weight")
    private BigDecimal subStoneWeight;

    @Schema(description = "圈口")
    @GoodsColumn(value = "circle_size")
    private String circleSize;

    @Schema(description = "成本单价(元)")
    @GoodsColumn(value = "cost_price")
    private BigDecimal costPrice;

    @Schema(description = "金进单价(元)")
    @GoodsColumn(value = "gold_price")
    private BigDecimal goldPrice;

    @Schema(description = "银进单价(元)")
    @GoodsColumn(value = "silver_price")
    private BigDecimal silverPrice;

    @Schema(description = "进工费单价(元)")
    @GoodsColumn(value = "work_price")
    private BigDecimal workPrice;

    @Schema(description = "证书费(元)")
    @GoodsColumn(value = "cert_price")
    private BigDecimal certPrice;

    @Schema(description = "工费单价(元)")
    @GoodsColumn(value = "sale_work_price")
    private BigDecimal saleWorkPrice;

    @Schema(description = "标签单价(元)")
    @GoodsColumn(value = "tag_price")
    private BigDecimal tagPrice;

    @Schema(description = "原始数量")
    @GoodsColumn(value = "num")
    private Integer num;

    @Schema(description = "库存数量")
    private Integer stockNum;

    @Schema(description = "采购退数量")
    private Integer returnNum;

    @Schema(description = "售出数量")
    private Integer soldNum;

    @Schema(description = "调拨中数量")
    private Integer transferNum;

    @Schema(description = "冻结数量")
    private Integer frozenNum;

    @Schema(description = "自定义字段")
    private List<CustomColumnItemDTO> customerColumns = new ArrayList<>();

    @Schema(description = "商品图片列表")
    @GoodsColumn(value = "image")
    private List<GoodsHasImagesEntity> images = new ArrayList<>();

    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getMainStoneWeight() {
        return PriceUtil.formatThreeDecimal(mainStoneWeight);
    }
    
    public BigDecimal getSubStoneWeight() {
        return PriceUtil.formatThreeDecimal(subStoneWeight);
    }
    
    public BigDecimal getCostPrice() {
        return PriceUtil.formatTwoDecimal(costPrice);
    }

    public BigDecimal getGoldPrice() {
        return PriceUtil.formatTwoDecimal(goldPrice);
    }

    public BigDecimal getSilverPrice() {
        return PriceUtil.formatTwoDecimal(silverPrice);
    }

    public BigDecimal getWorkPrice() {
        return PriceUtil.formatTwoDecimal(workPrice);
    }

    public BigDecimal getCertPrice() {
        return PriceUtil.formatTwoDecimal(certPrice);
    }

    public BigDecimal getSaleWorkPrice() {
        return PriceUtil.formatTwoDecimal(saleWorkPrice);
    }

    public BigDecimal getTagPrice() {
        return PriceUtil.formatTwoDecimal(tagPrice);
    }
}
